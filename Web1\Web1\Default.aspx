<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="Web1.Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>宠物商店 - 欢迎页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            display: inline-block;
            margin: 10px 15px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .nav-links a:hover {
            background-color: #0056b3;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .test-accounts {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🐾 欢迎来到宠物商店 🐾</h1>

            <div class="info-box">
                <h3>项目说明</h3>
                <p>这是一个基于ASP.NET Web Forms的宠物商店网站，实现了基本的电商功能。</p>
                <p>包含用户注册登录、商品浏览、分类查看、后台管理等功能。</p>
            </div>

            <div class="nav-links">
                <a href="index.aspx">进入网站首页</a>
                <a href="login.aspx">用户登录</a>
                <a href="userAdd.aspx">用户注册</a>
                <a href="allproduct.aspx">浏览商品</a>
            </div>

            <div class="test-accounts">
                <h3>测试账户</h3>
                <p><strong>管理员账户：</strong>用户名 <code>admin</code>，密码 <code>123</code></p>
                <p><strong>普通用户：</strong>用户名 <code>Jack</code>，密码 <code>123</code></p>
                <p><em>注意：当前版本使用硬编码账户，无需数据库配置即可测试基本功能。</em></p>
            </div>

            <div class="info-box">
                <h3>功能特性</h3>
                <ul>
                    <li>✅ 用户注册和登录系统</li>
                    <li>✅ 商品分类浏览</li>
                    <li>✅ 商品搜索功能</li>
                    <li>✅ 个人信息管理</li>
                    <li>✅ 后台管理系统（管理员专用）</li>
                    <li>✅ 响应式界面设计</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>© 2025 宠物商店. 基于ASP.NET Web Forms开发</p>
            </div>
        </div>
    </form>
</body>
</html>
