<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="userAdd.aspx.cs" Inherits="Web1.userAdd" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>用户注册</h2>
    
    <table cellpadding="5" cellspacing="0" style="margin: 20px 0;">
        <tr>
            <td>用户名:</td>
            <td>
                <asp:TextBox ID="txt_username" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" 
                    ControlToValidate="txt_username" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td>密码:</td>
            <td>
                <asp:TextBox ID="txt_password" runat="server" TextMode="Password" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" 
                    ControlToValidate="txt_password" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td>确认密码:</td>
            <td>
                <asp:TextBox ID="txt_confirm_password" runat="server" TextMode="Password" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" 
                    ControlToValidate="txt_confirm_password" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
                <asp:CompareValidator ID="CompareValidator1" runat="server" 
                    ControlToValidate="txt_confirm_password" ControlToCompare="txt_password" 
                    ErrorMessage="密码不一致" ForeColor="Red"></asp:CompareValidator>
            </td>
        </tr>
        <tr>
            <td>邮箱:</td>
            <td>
                <asp:TextBox ID="txt_email" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" 
                    ControlToValidate="txt_email" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
                <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" 
                    ControlToValidate="txt_email" ErrorMessage="邮箱格式不正确" 
                    ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" 
                    ForeColor="Red"></asp:RegularExpressionValidator>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding-top: 15px;">
                <asp:Button ID="btn_register" runat="server" OnClick="btn_register_Click" Text="注册" 
                    Width="80px" />
                &nbsp;&nbsp;
                <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回登录" 
                    CausesValidation="false" Width="80px" />
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding-top: 10px;">
                <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>
            </td>
        </tr>
    </table>
</asp:Content>
