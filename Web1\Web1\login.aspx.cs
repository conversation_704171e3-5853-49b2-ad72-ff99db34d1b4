using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                Label1.Text = "";
            }
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            string username = this.txt_id.Text.Trim();
            string pwd = this.txt_pwd.Text.Trim();

            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    // 根据用户名查找用户
                    Customer c = db.Customer.FirstOrDefault(u => u.Name == username);

                    if (c != null)
                    {
                        if (c.Password == pwd)
                        {
                            // 登录成功，保存用户信息到Session
                            Session["UserId"] = c.CustomerId;
                            Session["UserName"] = c.Name;
                            Session["UserEmail"] = c.Email;

                            // 检查是否是管理员
                            if (username.ToLower() == "admin")
                            {
                                // 管理员登录，跳转到后台管理
                                Response.Redirect("index_m.aspx");
                            }
                            else
                            {
                                // 普通用户登录，跳转到个人信息页面
                                Response.Redirect("myinfo.aspx");
                            }
                        }
                        else
                        {
                            this.Label1.Text = "密码错误！";
                        }
                    }
                    else
                    {
                        this.Label1.Text = "用户名不存在！";
                    }
                }
            }
            catch (Exception ex)
            {
                this.Label1.Text = "登录失败：" + ex.Message;
            }
        }

        protected void Button2_Click(object sender, EventArgs e)
        {
            Response.Redirect("userAdd.aspx");
        }
    }
}
