using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lbl_message.Text = "";
            }
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            string username = this.txt_id.Text.Trim();
            string pwd = this.txt_pwd.Text.Trim();

            // 简化登录逻辑，使用硬编码的测试账户
            if (username == "admin" && pwd == "123")
            {
                // 管理员登录成功
                Session["UserId"] = 1;
                Session["UserName"] = "admin";
                Session["UserEmail"] = "<EMAIL>";
                Response.Redirect("index_m.aspx");
            }
            else if (username == "Jack" && pwd == "123")
            {
                // 普通用户登录成功
                Session["UserId"] = 2;
                Session["UserName"] = "Jack";
                Session["UserEmail"] = "<EMAIL>";
                Response.Redirect("myinfo.aspx");
            }
            else
            {
                this.lbl_message.Text = "用户名或密码错误！请使用测试账户：admin/123 或 Jack/123";
            }

            /*
            // 数据库版本的登录代码（需要配置数据库后启用）
            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    // 根据用户名查找用户
                    Customer c = db.Customer.FirstOrDefault(u => u.Name == username);

                    if (c != null)
                    {
                        if (c.Password == pwd)
                        {
                            // 登录成功，保存用户信息到Session
                            Session["UserId"] = c.CustomerId;
                            Session["UserName"] = c.Name;
                            Session["UserEmail"] = c.Email;

                            // 检查是否是管理员
                            if (username.ToLower() == "admin")
                            {
                                // 管理员登录，跳转到后台管理
                                Response.Redirect("index_m.aspx");
                            }
                            else
                            {
                                // 普通用户登录，跳转到个人信息页面
                                Response.Redirect("myinfo.aspx");
                            }
                        }
                        else
                        {
                            this.lbl_message.Text = "密码错误！";
                        }
                    }
                    else
                    {
                        this.lbl_message.Text = "用户名不存在！";
                    }
                }
            }
            catch (Exception ex)
            {
                this.lbl_message.Text = "登录失败：" + ex.Message;
            }
            */
        }

        protected void Button2_Click(object sender, EventArgs e)
        {
            Response.Redirect("userAdd.aspx");
        }
    }
}
