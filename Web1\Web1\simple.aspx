<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="simple.aspx.cs" Inherits="Web1.simple" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>宠物商店 - 简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; }
        .nav-links { text-align: center; margin: 20px 0; }
        .nav-links a { display: inline-block; margin: 10px 15px; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🐾 宠物商店网站 - 简单测试页面 🐾</h1>
            
            <div class="nav-links">
                <a href="demo.html">静态界面演示</a>
                <a href="Default.aspx">欢迎页面</a>
            </div>
            
            <div class="test-section">
                <h3>登录测试</h3>
                <table>
                    <tr>
                        <td>用户名:</td>
                        <td><asp:TextBox ID="txtUsername" runat="server" Width="200px"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td>密码:</td>
                        <td><asp:TextBox ID="txtPassword" runat="server" TextMode="Password" Width="200px"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <asp:Button ID="btnLogin" runat="server" Text="登录" OnClick="btnLogin_Click" />
                            <asp:Button ID="btnClear" runat="server" Text="清除" OnClick="btnClear_Click" />
                        </td>
                    </tr>
                </table>
                <br />
                <asp:Label ID="lblLoginResult" runat="server" Text=""></asp:Label>
            </div>
            
            <div class="test-section">
                <h3>测试账户</h3>
                <p><strong>管理员：</strong>admin / 123</p>
                <p><strong>普通用户：</strong>Jack / 123</p>
            </div>
            
            <div class="test-section">
                <h3>项目状态</h3>
                <p class="success">✅ 基本页面结构完成</p>
                <p class="success">✅ 静态界面演示可用</p>
                <p class="success">✅ 简单登录功能可用</p>
            </div>
        </div>
    </form>
</body>
</html>
