<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="login.aspx.cs" Inherits="Web1.login" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>用户登录</h2>
    
    <table cellpadding="5" cellspacing="0" style="margin: 20px 0;">
        <tr>
            <td>用户名:</td>
            <td>
                <asp:TextBox ID="txt_id" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" 
                    ControlToValidate="txt_id" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td>密码:</td>
            <td>
                <asp:TextBox ID="txt_pwd" runat="server" TextMode="Password" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" 
                    ControlToValidate="txt_pwd" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding-top: 15px;">
                <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="登录" 
                    Width="80px" />
                &nbsp;&nbsp;
                <asp:Button ID="Button2" runat="server" OnClick="Button2_Click" Text="注册" 
                    CausesValidation="false" Width="80px" />
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding-top: 10px;">
                <asp:Label ID="Label1" runat="server" Text="" ForeColor="Red"></asp:Label>
            </td>
        </tr>
    </table>
    
    <div style="margin-top: 20px; padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;">
        <h4>测试账户：</h4>
        <p>管理员账户：用户名 <strong>admin</strong>，密码 <strong>123</strong></p>
        <p>普通用户：用户名 <strong>Jack</strong>，密码 <strong>123</strong></p>
    </div>
</asp:Content>
