using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class allproduct : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadAllProducts();
            }
        }

        private void LoadAllProducts()
        {
            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    var products = db.Product.ToList();
                    if (products.Count > 0)
                    {
                        DataList1.DataSource = products;
                        DataList1.DataBind();
                    }
                    else
                    {
                        lblMessage.Text = "暂无商品信息";
                    }
                }
            }
            catch (Exception ex)
            {
                lblMessage.Text = "加载商品失败：" + ex.Message;
            }
        }
    }
}
