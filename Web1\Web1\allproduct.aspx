<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="allproduct.aspx.cs" Inherits="Web1.allproduct" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>所有商品</h2>
    
    <asp:DataList ID="DataList1" runat="server" RepeatColumns="4" CellPadding="10">
        <ItemTemplate>
            <div style="text-align: center; border: 1px solid #ccc; padding: 10px; margin: 5px; width: 120px;">
                <asp:Image ID="Image1" runat="server" Height="80px" Width="80px" 
                    ImageUrl='<%# Eval("Image") %>' />
                <br />
                <asp:HyperLink ID="HyperLink8" runat="server" 
                    NavigateUrl='<%# "productDetail.aspx?pid=" + Eval("ProductId") %>' 
                    Text='<%# Eval("Name") %>'></asp:HyperLink>
                <br />
                <strong>￥<asp:Label ID="Label2" runat="server" Text='<%# Eval("ListPrice", "{0:F2}") %>'></asp:Label></strong>
            </div>
        </ItemTemplate>
    </asp:DataList>
    
    <div style="margin-top: 20px;">
        <asp:Label ID="lblMessage" runat="server" Text="" ForeColor="Red"></asp:Label>
    </div>
</asp:Content>
