<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="index.aspx.cs" Inherits="Web1.index" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>欢迎来到宠物商店！</h2>
    <p>这里有各种可爱的宠物等着您！</p>
    
    <h3>热门商品</h3>
    <asp:DataList ID="DataList1" runat="server" RepeatColumns="4" CellPadding="10">
        <ItemTemplate>
            <div style="text-align: center; border: 1px solid #ccc; padding: 10px; margin: 5px;">
                <asp:Image ID="Image1" runat="server" Height="80px" Width="80px" 
                    ImageUrl='<%# Eval("Image") %>' />
                <br />
                <asp:HyperLink ID="HyperLink1" runat="server" 
                    NavigateUrl='<%# "productDetail.aspx?pid=" + Eval("ProductId") %>' 
                    Text='<%# Eval("Name") %>'></asp:HyperLink>
                <br />
                <strong>￥<asp:Label ID="Label1" runat="server" Text='<%# Eval("ListPrice", "{0:F2}") %>'></asp:Label></strong>
            </div>
        </ItemTemplate>
    </asp:DataList>
    
    <br />
    <p><asp:HyperLink ID="lnkAllProducts" runat="server" NavigateUrl="~/allproduct.aspx">查看所有商品 &gt;&gt;</asp:HyperLink></p>
</asp:Content>
