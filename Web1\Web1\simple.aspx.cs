using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class simple : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lblLoginResult.Text = "请输入用户名和密码进行测试";
                lblLoginResult.ForeColor = System.Drawing.Color.Blue;
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                lblLoginResult.Text = "请输入用户名和密码";
                lblLoginResult.ForeColor = System.Drawing.Color.Red;
                return;
            }

            if (username == "admin" && password == "123")
            {
                lblLoginResult.Text = "管理员登录成功！";
                lblLoginResult.ForeColor = System.Drawing.Color.Green;
                Session["UserName"] = "admin";
                Session["UserType"] = "admin";
            }
            else if (username == "Jack" && password == "123")
            {
                lblLoginResult.Text = "用户登录成功！";
                lblLoginResult.ForeColor = System.Drawing.Color.Green;
                Session["UserName"] = "Jack";
                Session["UserType"] = "user";
            }
            else
            {
                lblLoginResult.Text = "用户名或密码错误！请使用测试账户：admin/123 或 Jack/123";
                lblLoginResult.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btnClear_Click(object sender, EventArgs e)
        {
            txtUsername.Text = "";
            txtPassword.Text = "";
            lblLoginResult.Text = "已清除输入内容";
            lblLoginResult.ForeColor = System.Drawing.Color.Gray;
            Session.Clear();
        }
    }
}
