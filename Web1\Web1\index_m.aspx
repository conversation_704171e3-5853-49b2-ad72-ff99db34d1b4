<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="index_m.aspx.cs" Inherits="Web1.index_m" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>后台管理系统</h2>
    
    <div style="margin: 20px 0;">
        <h3>欢迎使用宠物商店管理系统！</h3>
        <p>您可以通过左侧菜单进行以下操作：</p>
        
        <ul>
            <li><strong>用户信息管理</strong>
                <ul>
                    <li>查看和管理用户列表</li>
                    <li>添加新用户</li>
                </ul>
            </li>
            <li><strong>商品信息管理</strong>
                <ul>
                    <li>查看和管理商品列表</li>
                    <li>添加新商品</li>
                    <li>管理商品分类</li>
                </ul>
            </li>
        </ul>
    </div>
    
    <div style="margin: 20px 0; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc;">
        <h4>系统统计信息</h4>
        <table cellpadding="5" cellspacing="0">
            <tr>
                <td>用户总数：</td>
                <td><asp:Label ID="lblUserCount" runat="server" Text="0"></asp:Label> 人</td>
            </tr>
            <tr>
                <td>商品总数：</td>
                <td><asp:Label ID="lblProductCount" runat="server" Text="0"></asp:Label> 件</td>
            </tr>
            <tr>
                <td>分类总数：</td>
                <td><asp:Label ID="lblCategoryCount" runat="server" Text="0"></asp:Label> 个</td>
            </tr>
        </table>
    </div>
    
    <div style="margin: 20px 0;">
        <asp:Button ID="btnBackToFront" runat="server" OnClick="btnBackToFront_Click" 
            Text="返回前台" Width="100px" />
    </div>
</asp:Content>
