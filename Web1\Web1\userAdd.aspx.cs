using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class userAdd : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lbl_message.Text = "";
            }
        }

        protected void btn_register_Click(object sender, EventArgs e)
        {
            string username = txt_username.Text.Trim();
            string password = txt_password.Text.Trim();
            string email = txt_email.Text.Trim();

            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    // 检查用户名是否已存在
                    Customer existingUser = db.Customer.FirstOrDefault(c => c.Name == username);
                    if (existingUser != null)
                    {
                        lbl_message.Text = "用户名已存在，请选择其他用户名！";
                        return;
                    }

                    // 检查邮箱是否已存在
                    Customer existingEmail = db.Customer.FirstOrDefault(c => c.Email == email);
                    if (existingEmail != null)
                    {
                        lbl_message.Text = "邮箱已被注册，请使用其他邮箱！";
                        return;
                    }

                    // 创建新用户
                    Customer newUser = new Customer
                    {
                        Name = username,
                        Password = password,
                        Email = email
                    };

                    db.Customer.Add(newUser);
                    db.SaveChanges();

                    lbl_message.ForeColor = System.Drawing.Color.Green;
                    lbl_message.Text = "注册成功！3秒后跳转到登录页面...";

                    // 3秒后跳转到登录页面
                    Response.AddHeader("Refresh", "3;URL=login.aspx");
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "注册失败：" + ex.Message;
            }
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            Response.Redirect("login.aspx");
        }
    }
}
