using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class index : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadFeaturedProducts();
            }
        }

        private void LoadFeaturedProducts()
        {
            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    // 获取前8个商品作为热门商品展示
                    var products = db.Product.Take(8).ToList();
                    DataList1.DataSource = products;
                    DataList1.DataBind();
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                System.Diagnostics.Debug.WriteLine("加载商品失败: " + ex.Message);
            }
        }
    }
}
