using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class test : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                UpdateSessionInfo();
            }
        }

        protected void btnSetSession_Click(object sender, EventArgs e)
        {
            Session["TestUser"] = "测试用户";
            Session["TestTime"] = DateTime.Now.ToString();
            UpdateSessionInfo();
        }

        protected void btnClearSession_Click(object sender, EventArgs e)
        {
            Session.Clear();
            UpdateSessionInfo();
        }

        protected void btnTest_Click(object sender, EventArgs e)
        {
            string input = txtTest.Text.Trim();
            if (string.IsNullOrEmpty(input))
            {
                lblTestResult.Text = "请输入测试文本";
                lblTestResult.ForeColor = System.Drawing.Color.Red;
            }
            else
            {
                lblTestResult.Text = "测试成功！您输入的内容是：" + input;
                lblTestResult.ForeColor = System.Drawing.Color.Green;
            }
        }

        private void UpdateSessionInfo()
        {
            if (Session["TestUser"] != null)
            {
                lblSessionInfo.Text = string.Format("Session信息：用户={0}, 时间={1}", 
                    Session["TestUser"], Session["TestTime"]);
                lblSessionInfo.ForeColor = System.Drawing.Color.Green;
            }
            else
            {
                lblSessionInfo.Text = "当前没有Session信息";
                lblSessionInfo.ForeColor = System.Drawing.Color.Gray;
            }
        }
    }
}
