# 宠物商店网站

这是一个基于ASP.NET Web Forms的宠物商店网站项目。

## 项目结构

```
Web1/
├── Web1.sln                    # 解决方案文件
├── Web1/
│   ├── Web1.csproj            # 项目文件
│   ├── Web.config             # 配置文件
│   ├── packages.config        # NuGet包配置
│   ├── Models/                # 数据模型
│   │   └── MyPetShopEntities.cs
│   ├── q.Master               # 前台主页面模板
│   ├── h.Master               # 后台主页面模板
│   ├── Images/                # 界面图片资源
│   ├── Prod_Images/           # 产品图片
│   ├── index.aspx             # 首页
│   ├── login.aspx             # 登录页面
│   ├── userAdd.aspx           # 用户注册
│   ├── myinfo.aspx            # 个人信息
│   ├── allproduct.aspx        # 所有商品
│   └── index_m.aspx           # 后台管理首页
└── Database_Scripts/
    └── Shop_sql2.sql          # 数据库脚本
```

## 运行步骤

### 1. 数据库设置
1. 确保安装了SQL Server或SQL Server Express
2. 运行 `Database_Scripts/Shop_sql2.sql` 脚本创建数据库和初始数据
3. 修改 `Web.config` 中的连接字符串以匹配您的数据库配置

### 2. 项目运行
1. 使用Visual Studio打开 `Web1.sln`
2. 还原NuGet包
3. 编译并运行项目

### 3. 测试账户
- **管理员账户**：用户名 `admin`，密码 `123`
- **普通用户**：用户名 `Jack`，密码 `123`

## 主要功能

### 前台功能
- 用户注册和登录
- 浏览商品
- 商品分类查看
- 商品搜索
- 个人信息管理

### 后台功能（管理员）
- 用户信息管理
- 商品信息管理
- 商品分类管理
- 系统统计信息

## 技术栈
- **框架**: ASP.NET Web Forms (.NET Framework 4.7.2)
- **数据库**: SQL Server
- **ORM**: Entity Framework 6.2.0
- **开发工具**: Visual Studio
- **服务器**: IIS Express

## 注意事项
1. 确保数据库连接字符串正确配置
2. 需要将相应的图片文件放置在 Images 和 Prod_Images 目录中
3. 项目使用Entity Framework Code First模式，首次运行时会自动创建数据库结构

## 界面预览
项目实现了基本的宠物商店界面，包括：
- 顶部导航栏（首页、所有商品、注册、登录、购物车、个人信息）
- 左侧商品分类菜单
- 主要内容区域
- 搜索功能
- 商品展示网格布局
