# 宠物商店网站

这是一个基于ASP.NET Web Forms的宠物商店网站项目。

## 项目结构

```
Web1/
├── Web1.sln                    # 解决方案文件
├── Web1/
│   ├── Web1.csproj            # 项目文件
│   ├── Web.config             # 配置文件
│   ├── packages.config        # NuGet包配置
│   ├── Models/                # 数据模型
│   │   └── MyPetShopEntities.cs
│   ├── q.Master               # 前台主页面模板
│   ├── h.Master               # 后台主页面模板
│   ├── Images/                # 界面图片资源
│   ├── Prod_Images/           # 产品图片
│   ├── index.aspx             # 首页
│   ├── login.aspx             # 登录页面
│   ├── userAdd.aspx           # 用户注册
│   ├── myinfo.aspx            # 个人信息
│   ├── allproduct.aspx        # 所有商品
│   └── index_m.aspx           # 后台管理首页
└── Database_Scripts/
    └── Shop_sql2.sql          # 数据库脚本
```

## 运行步骤

### 1. 数据库设置
1. 确保安装了SQL Server或SQL Server Express
2. 运行 `Database_Scripts/Shop_sql2.sql` 脚本创建数据库和初始数据
3. 修改 `Web.config` 中的连接字符串以匹配您的数据库配置

### 2. 项目运行
1. 使用Visual Studio打开 `Web1.sln`
2. 还原NuGet包
3. 编译并运行项目

### 3. 测试账户
- **管理员账户**：用户名 `admin`，密码 `123`
- **普通用户**：用户名 `Jack`，密码 `123`

## 主要功能

### 前台功能
- 用户注册和登录
- 浏览商品
- 商品分类查看
- 商品搜索
- 个人信息管理

### 后台功能（管理员）
- 用户信息管理
- 商品信息管理
- 商品分类管理
- 系统统计信息

## 技术栈
- **框架**: ASP.NET Web Forms (.NET Framework 4.7.2)
- **数据库**: SQL Server
- **ORM**: Entity Framework 6.2.0
- **开发工具**: Visual Studio
- **服务器**: IIS Express

## 当前状态
✅ **已完成**：
- 项目结构和配置文件
- 所有页面的基本框架
- 用户界面布局（与提供的图片一致）
- 硬编码的登录功能（admin/123, Jack/123）
- 页面导航和基本交互

⚠️ **需要配置**：
- SQL Server数据库
- 运行初始化脚本
- 启用数据库相关功能

## 测试页面
- `Default.aspx` - 项目欢迎页面
- `test.aspx` - 功能测试页面
- `demo.html` - 静态界面演示

## 注意事项
1. 当前版本使用硬编码账户，无需数据库即可测试基本功能
2. 数据库相关代码已注释，配置数据库后可取消注释启用
3. 需要将相应的图片文件放置在 Images 和 Prod_Images 目录中
4. 项目已修复所有编译错误，可以直接在Visual Studio中运行

## 界面预览
项目实现了基本的宠物商店界面，包括：
- 顶部导航栏（首页、所有商品、注册、登录、购物车、个人信息）
- 左侧商品分类菜单
- 主要内容区域
- 搜索功能
- 商品展示网格布局

## 快速开始
1. 用Visual Studio打开 `Web1.sln`
2. 按F5运行项目
3. 访问 `Default.aspx` 查看欢迎页面
4. 访问 `demo.html` 查看静态界面演示
5. 使用测试账户登录体验功能
