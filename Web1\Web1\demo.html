<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>宠物商店 - 界面演示</title>
    <style type="text/css">
        .auto-style1 {
            width: 800px;
            margin: 0 auto;
        }
        .auto-style2 {
            width: 100%;
        }
        .auto-style3 {
            width: 78px;
        }
        .auto-style5 {
            background-color: #00FFCC;
            padding: 5px;
            text-align: center;
        }
        .auto-style6 {
            color: #FFFFFF;
            background-color: #99CC00;
            padding: 5px;
        }
        .auto-style7 {
            background-color: aliceblue;
            padding: 10px;
            text-align: center;
        }
        .auto-left {
            background-color: cornsilk;
            width: 200px;
            height: 400px;
            padding: 10px;
            vertical-align: top;
        }
        .auto-right {
            width: 600px;
            height: 400px;
            padding: 10px;
            vertical-align: top;
        }
        .auto-style8 {
            width: 100%;
            background-color: #CCFFFF;
            padding: 5px;
        }
        .auto-style9 {
            width: 60px;
            height: 20px;
        }
        .auto-style11 {
            height: 20px;
        }
        .auto-style12 {
            width: 65px;
            height: 31px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        .product-item {
            text-align: center;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: white;
        }
        .product-item img {
            width: 80px;
            height: 80px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            display: block;
            margin: 0 auto 5px auto;
        }
        .product-item a {
            text-decoration: none;
            color: #0066cc;
        }
        .product-item a:hover {
            text-decoration: underline;
        }
        .price {
            font-weight: bold;
            color: #ff6600;
        }
        .category-link {
            display: block;
            margin: 5px 0;
            color: #0066cc;
            text-decoration: none;
        }
        .category-link:hover {
            text-decoration: underline;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div>
        <table cellpadding="0" cellspacing="0" class="auto-style1">
            <tr>
                <td colspan="2">
                    <table cellpadding="0" cellspacing="0" class="auto-style2">
                        <tr>
                            <td class="auto-style3">
                                <div class="auto-style12" style="background-color: #ddd; text-align: center; line-height: 31px;">LOGO</div>
                            </td>
                            <td>
                                <table cellpadding="0" cellspacing="0" class="auto-style2">
                                    <tr>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">首页</a>
                                        </td>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">所有商品</a>
                                        </td>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">注册</a>
                                        </td>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">登录</a>
                                        </td>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">购物车</a>
                                        </td>
                                        <td class="auto-style5">
                                            <a href="#" style="color: black; text-decoration: none;">个人信息</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="6" class="auto-style6">
                                            您还没有登录
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="2">您的位置：商品分类</td>
            </tr>
            <tr>
                <td class="auto-left">
                    <strong>商品分类</strong><br /><br />
                    <a href="#" class="category-link">Fish</a>
                    <a href="#" class="category-link">Bugs</a>
                    <a href="#" class="category-link">Backyard</a>
                    <a href="#" class="category-link">Birds</a>
                    <a href="#" class="category-link">Endangered</a>
                </td>
                <td class="auto-right">
                    <table cellpadding="0" cellspacing="0" class="auto-style8">
                        <tr>
                            <td class="auto-style11">
                                <div class="auto-style9" style="background-color: #ddd; display: inline-block; text-align: center; line-height: 20px;">搜索</div>
                                <input type="text" style="width: 200px;" placeholder="搜索商品...">
                                <button type="button">搜索</button>
                            </td>
                        </tr>
                    </table>
                    <br />
                    
                    <h3>热门商品</h3>
                    <div class="product-grid">
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Meno</a><br />
                            <span class="price">12.10</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Butterfly</a><br />
                            <span class="price">24.70</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Domestic</a><br />
                            <span class="price">45.50</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Pointy</a><br />
                            <span class="price">35.55</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Eucalyptus</a><br />
                            <span class="price">28.50</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Cat</a><br />
                            <span class="price">38.50</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Flowerloving</a><br />
                            <span class="price">25.20</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Fish</a><br />
                            <span class="price">55.00</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Ant</a><br />
                            <span class="price">23.40</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Zebra</a><br />
                            <span class="price">40.40</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">Panda</a><br />
                            <span class="price">47.70</span>
                        </div>
                        <div class="product-item">
                            <div style="width: 80px; height: 80px; background-color: #e0e0e0; margin: 0 auto 5px; line-height: 80px; text-align: center;">图片</div>
                            <a href="#">?</a><br />
                            <span class="price">0.00</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="auto-style7">&nbsp;</td>
                <td class="auto-style7">Copyright 2025</td>
            </tr>
        </table>
    </div>
</body>
</html>
