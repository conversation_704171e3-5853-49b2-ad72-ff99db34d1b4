using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class index_m : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadStatistics();
            }
        }

        private void LoadStatistics()
        {
            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    // 统计用户数量
                    int userCount = db.Customer.Count();
                    lblUserCount.Text = userCount.ToString();

                    // 统计商品数量
                    int productCount = db.Product.Count();
                    lblProductCount.Text = productCount.ToString();

                    // 统计分类数量
                    int categoryCount = db.Category.Count();
                    lblCategoryCount.Text = categoryCount.ToString();
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                System.Diagnostics.Debug.WriteLine("加载统计信息失败: " + ex.Message);
            }
        }

        protected void btnBackToFront_Click(object sender, EventArgs e)
        {
            Response.Redirect("index.aspx");
        }
    }
}
