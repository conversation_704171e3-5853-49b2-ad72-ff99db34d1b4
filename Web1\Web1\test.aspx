<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test.aspx.cs" Inherits="Web1.test" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>宠物商店 - 功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>宠物商店网站 - 功能测试页面</h1>
        
        <div class="test-section">
            <h3>页面链接测试</h3>
            <p><a href="Default.aspx">默认欢迎页面</a></p>
            <p><a href="index.aspx">网站首页</a></p>
            <p><a href="login.aspx">用户登录</a></p>
            <p><a href="userAdd.aspx">用户注册</a></p>
            <p><a href="allproduct.aspx">所有商品</a></p>
            <p><a href="demo.html">静态界面演示</a></p>
        </div>
        
        <div class="test-section">
            <h3>Session测试</h3>
            <asp:Button ID="btnSetSession" runat="server" Text="设置测试Session" OnClick="btnSetSession_Click" />
            <asp:Button ID="btnClearSession" runat="server" Text="清除Session" OnClick="btnClearSession_Click" />
            <br /><br />
            <asp:Label ID="lblSessionInfo" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>控件测试</h3>
            <asp:TextBox ID="txtTest" runat="server" placeholder="输入测试文本"></asp:TextBox>
            <asp:Button ID="btnTest" runat="server" Text="测试按钮" OnClick="btnTest_Click" />
            <br /><br />
            <asp:Label ID="lblTestResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>项目状态</h3>
            <p class="success">✅ 项目结构创建完成</p>
            <p class="success">✅ 页面模板创建完成</p>
            <p class="success">✅ 基本页面创建完成</p>
            <p class="success">✅ 控件绑定修复完成</p>
            <p class="info">ℹ️ 数据库功能已注释，使用硬编码测试数据</p>
            <p class="info">ℹ️ 可以配置数据库后启用完整功能</p>
        </div>
        
        <div class="test-section">
            <h3>下一步操作</h3>
            <ol>
                <li>配置SQL Server数据库</li>
                <li>运行Database_Scripts/Shop_sql2.sql脚本</li>
                <li>取消代码中的数据库相关注释</li>
                <li>添加产品图片到Prod_Images文件夹</li>
                <li>测试完整功能</li>
            </ol>
        </div>
    </form>
</body>
</html>
