using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class q : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadCategories();
                UpdateLoginStatus();
            }
        }

        private void LoadCategories()
        {
            try
            {
                using (MyPetShopEntities db = new MyPetShopEntities())
                {
                    var categories = db.Category.ToList();
                    rptCategories.DataSource = categories;
                    rptCategories.DataBind();
                }
            }
            catch (Exception ex)
            {
                // 处理异常，可以记录日志或显示错误信息
                System.Diagnostics.Debug.WriteLine("加载分类失败: " + ex.Message);
            }
        }

        private void UpdateLoginStatus()
        {
            if (Session["UserName"] != null)
            {
                Label1.Text = "欢迎您，" + Session["UserName"].ToString() + "！";
                Label1.CssClass = "auto-style6";
            }
            else
            {
                Label1.Text = "您还没有登录";
                Label1.CssClass = "auto-style6";
            }
        }

        protected void ImageButton1_Click(object sender, ImageClickEventArgs e)
        {
            string searchKeyword = this.TextBox1.Text.Trim();
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                Response.Redirect("productSearch.aspx?pn=" + Server.UrlEncode(searchKeyword));
            }
        }
    }
}
