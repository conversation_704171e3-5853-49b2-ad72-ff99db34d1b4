using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class q : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadCategories();
                UpdateLoginStatus();
            }
        }

        private void LoadCategories()
        {
            // 分类链接已在页面中静态定义，无需动态加载
        }

        private void UpdateLoginStatus()
        {
            if (Session["UserName"] != null)
            {
                Label1.Text = "欢迎您，" + Session["UserName"].ToString() + "！";
                Label1.CssClass = "auto-style6";
            }
            else
            {
                Label1.Text = "您还没有登录";
                Label1.CssClass = "auto-style6";
            }
        }

        protected void ImageButton1_Click(object sender, ImageClickEventArgs e)
        {
            string searchKeyword = this.TextBox1.Text.Trim();
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                Response.Redirect("productSearch.aspx?pn=" + Server.UrlEncode(searchKeyword));
            }
        }
    }
}
