using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class h : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 检查管理员登录状态
                CheckAdminLogin();
            }
        }

        private void CheckAdminLogin()
        {
            if (Session["UserName"] != null)
            {
                string userName = Session["UserName"].ToString();
                if (userName.ToLower() == "admin")
                {
                    lblAdminStatus.Text = "欢迎管理员：" + userName;
                }
                else
                {
                    lblAdminStatus.Text = "权限不足，请使用管理员账户登录";
                    Response.Redirect("~/login.aspx");
                }
            }
            else
            {
                lblAdminStatus.Text = "请先登录管理员账户";
                Response.Redirect("~/login.aspx");
            }
        }
    }
}
