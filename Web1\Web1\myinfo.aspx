<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="myinfo.aspx.cs" Inherits="Web1.myinfo" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>个人信息</h2>

    <table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse; margin: 20px 0;">
        <tr>
            <td style="background-color: #f0f0f0; font-weight: bold;">用户ID:</td>
            <td><asp:Label ID="lbl_userid" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td style="background-color: #f0f0f0; font-weight: bold;">用户名:</td>
            <td><asp:Label ID="lbl_username" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td style="background-color: #f0f0f0; font-weight: bold;">邮箱:</td>
            <td><asp:Label ID="lbl_email" runat="server" Text=""></asp:Label></td>
        </tr>
    </table>

    <div style="margin: 20px 0;">
        <asp:Button ID="btn_admin" runat="server" OnClick="btn_admin_Click" Text="登入到后台" 
            Visible="false" Width="100px" />
        &nbsp;&nbsp;
        <asp:Button ID="btn_logout" runat="server" OnClick="btn_logout_Click" Text="退出登录" 
            Width="100px" />
        &nbsp;&nbsp;
        <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回首页" 
            Width="100px" />
    </div>

    <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>
</asp:Content>
