using System;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Web1
{
    public partial class MyPetShopEntities : DbContext
    {
        public MyPetShopEntities()
            : base("name=MyPetShopEntities")
        {
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }

        public virtual DbSet<CartItem> CartItem { get; set; }
        public virtual DbSet<Category> Category { get; set; }
        public virtual DbSet<Customer> Customer { get; set; }
        public virtual DbSet<Order> Order { get; set; }
        public virtual DbSet<OrderItem> OrderItem { get; set; }
        public virtual DbSet<Product> Product { get; set; }
        public virtual DbSet<Supplier> Supplier { get; set; }
    }

    [Table("Category")]
    public partial class Category
    {
        [Key]
        public int CategoryId { get; set; }

        [StringLength(80)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Descn { get; set; }
    }

    [Table("Customer")]
    public partial class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [Required]
        [StringLength(80)]
        public string Name { get; set; }

        [Required]
        [StringLength(80)]
        public string Password { get; set; }

        [Required]
        [StringLength(80)]
        public string Email { get; set; }
    }

    [Table("Product")]
    public partial class Product
    {
        [Key]
        public int ProductId { get; set; }

        public int CategoryId { get; set; }

        [Column(TypeName = "decimal")]
        public decimal? ListPrice { get; set; }

        [Column(TypeName = "decimal")]
        public decimal? UnitCost { get; set; }

        public int? SuppId { get; set; }

        [StringLength(80)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Descn { get; set; }

        [StringLength(80)]
        public string Image { get; set; }

        public int Qty { get; set; }

        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }

        [ForeignKey("SuppId")]
        public virtual Supplier Supplier { get; set; }
    }

    [Table("Order")]
    public partial class Order
    {
        [Key]
        public int OrderId { get; set; }

        public int CustomerId { get; set; }

        [Required]
        [StringLength(80)]
        public string UserName { get; set; }

        public DateTime OrderDate { get; set; }

        [StringLength(80)]
        public string Addr1 { get; set; }

        [StringLength(80)]
        public string Addr2 { get; set; }

        [StringLength(80)]
        public string City { get; set; }

        [StringLength(80)]
        public string State { get; set; }

        [StringLength(6)]
        public string Zip { get; set; }

        [StringLength(40)]
        public string Phone { get; set; }

        [StringLength(10)]
        public string Status { get; set; }

        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; }
    }

    [Table("OrderItem")]
    public partial class OrderItem
    {
        [Key]
        public int ItemId { get; set; }

        public int OrderId { get; set; }

        [StringLength(80)]
        public string ProName { get; set; }

        [Column(TypeName = "decimal")]
        public decimal? ListPrice { get; set; }

        public int Qty { get; set; }

        [Column(TypeName = "decimal")]
        public decimal? TotalPrice { get; set; }

        [ForeignKey("OrderId")]
        public virtual Order Order { get; set; }
    }

    [Table("CartItem")]
    public partial class CartItem
    {
        [Key]
        public int CartItemId { get; set; }

        public int CustomerId { get; set; }

        public int ProId { get; set; }

        [Required]
        [StringLength(80)]
        public string ProName { get; set; }

        [Column(TypeName = "decimal")]
        public decimal ListPrice { get; set; }

        public int Qty { get; set; }

        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; }

        [ForeignKey("ProId")]
        public virtual Product Product { get; set; }
    }

    [Table("Supplier")]
    public partial class Supplier
    {
        [Key]
        public int SuppId { get; set; }

        [StringLength(80)]
        public string Name { get; set; }

        [StringLength(80)]
        public string Addr1 { get; set; }

        [StringLength(80)]
        public string Addr2 { get; set; }

        [StringLength(80)]
        public string City { get; set; }

        [StringLength(80)]
        public string State { get; set; }

        [StringLength(6)]
        public string Zip { get; set; }

        [StringLength(40)]
        public string Phone { get; set; }
    }
}
